package org.springblade.modules.beachwaste.typehandler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.Point;
import org.postgis.PGgeometry;
import org.postgis.jts.JtsWrapper;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * MyBatis TypeHandler for JTS Point <-> PostGIS GEOMETRY
 * 用于处理JTS Point类型与PostGIS几何类型之间的转换
 */
@MappedJdbcTypes(JdbcType.OTHER) // 告诉MyBatis这个Handler处理所有未知的JDBC类型
@MappedTypes(Point.class)       // 告诉MyBatis这个Handler处理Java的Point类型
public class PointTypeHandler extends BaseTypeHandler<Point> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Point parameter, JdbcType jdbcType) throws SQLException {
        PGgeometry geometry = new PGgeometry(JtsWrapper.toLinearRing(parameter));
        ps.setObject(i, geometry);
    }

    @Override
    public Point getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Object obj = rs.getObject(columnName);
        if (obj == null) {
            return null;
        }
        return toPoint(obj);
    }

    @Override
    public Point getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Object obj = rs.getObject(columnIndex);
        if (obj == null) {
            return null;
        }
        return toPoint(obj);
    }

    @Override
    public Point getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Object obj = cs.getObject(columnIndex);
        if (obj == null) {
            return null;
        }
        return toPoint(obj);
    }

    private Point toPoint(Object obj) throws SQLException {
        if (obj instanceof PGgeometry) {
            Geometry geometry = ((PGgeometry) obj).getGeometry();
            if (geometry instanceof Point) {
                return (Point) geometry;
            }
        }
        // 可以添加对其他可能的返回类型的处理，例如 WKT 字符串
        return null;
    }
}