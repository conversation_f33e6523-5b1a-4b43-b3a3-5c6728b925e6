package org.springblade.modules.beachwaste.typehandler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.io.WKTWriter;
import org.postgis.PGgeometry;
import org.postgis.jts.JtsWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * MyBatis TypeHandler for JTS Point <-> PostGIS GEOMETRY
 * 用于处理JTS Point类型与PostGIS几何类型之间的转换
 */
@MappedJdbcTypes(JdbcType.OTHER)
@MappedTypes(Point.class)
public class PointTypeHandler extends BaseTypeHandler<Point> {

    private static final Logger logger = LoggerFactory.getLogger(PointTypeHandler.class);
    private static final WKTReader wktReader = new WKTReader();
    private static final WKTWriter wktWriter = new WKTWriter();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Point parameter, JdbcType jdbcType) throws SQLException {
        try {
            PGgeometry pgGeometry = new PGgeometry(JtsWrapper.wrap(parameter));
            ps.setObject(i, pgGeometry);
            logger.debug("设置Point参数: {}", parameter);
        } catch (Exception e) {
            logger.error("设置Point参数时发生错误: {}", e.getMessage(), e);
            throw new SQLException("无法将Point转换为PGgeometry", e);
        }
    }

    @Override
    public Point getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Object obj = rs.getObject(columnName);
        if (obj == null) {
            return null;
        }
        return toPoint(obj, columnName);
    }

    @Override
    public Point getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Object obj = rs.getObject(columnIndex);
        if (obj == null) {
            return null;
        }
        return toPoint(obj, "索引" + columnIndex);
    }

    @Override
    public Point getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Object obj = cs.getObject(columnIndex);
        if (obj == null) {
            return null;
        }
        return toPoint(obj, "CallableStatement索引" + columnIndex);
    }

    /**
     * 将数据库对象转换为JTS Point
     * @param obj 数据库返回的对象
     * @param context 上下文信息，用于日志记录
     * @return JTS Point对象
     * @throws SQLException 转换失败时抛出
     */
    private Point toPoint(Object obj, String context) throws SQLException {
        if (obj == null) {
            return null;
        }

        try {
            if (obj instanceof PGgeometry) {
                PGgeometry pgGeometry = (PGgeometry) obj;
                Geometry geometry = JtsWrapper.unwrap(pgGeometry);

                if (geometry instanceof Point) {
                    logger.debug("从{}成功转换Point: {}", context, geometry);
                    return (Point) geometry;
                } else {
                    logger.warn("从{}获取的几何对象不是Point类型: {}", context, geometry.getGeometryType());
                    throw new SQLException("期望Point类型，但获取到: " + geometry.getGeometryType());
                }
            } else if (obj instanceof String) {
                // 处理WKT字符串格式
                String wktString = (String) obj;
                logger.debug("尝试从WKT字符串解析Point: {}", wktString);

                Geometry geometry = wktReader.read(wktString);
                if (geometry instanceof Point) {
                    logger.debug("从{}的WKT字符串成功转换Point: {}", context, geometry);
                    return (Point) geometry;
                } else {
                    logger.warn("WKT字符串不是Point类型: {}", geometry.getGeometryType());
                    throw new SQLException("WKT字符串不是Point类型: " + geometry.getGeometryType());
                }
            } else {
                logger.error("不支持的数据库对象类型: {} (来自{})", obj.getClass().getName(), context);
                throw new SQLException("不支持的数据库对象类型: " + obj.getClass().getName());
            }
        } catch (ParseException e) {
            logger.error("解析WKT字符串时发生错误 (来自{}): {}", context, e.getMessage(), e);
            throw new SQLException("解析WKT字符串失败", e);
        } catch (Exception e) {
            logger.error("转换Point时发生未知错误 (来自{}): {}", context, e.getMessage(), e);
            throw new SQLException("转换Point时发生错误", e);
        }
    }
}
