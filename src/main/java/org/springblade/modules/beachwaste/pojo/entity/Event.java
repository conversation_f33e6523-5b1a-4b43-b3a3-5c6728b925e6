package org.springblade.modules.beachwaste.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.locationtech.jts.geom.Point;
import org.springblade.core.mp.base.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 事件信息表
 * <AUTHOR>
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("event")
public class Event extends BaseEntity {

    /**
     * 事件唯一标识符
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 事件发现时间（UTC+8）
     */
    private Date discoveryTime;

    /**
     * 垃圾材质类型（对应业务层枚举）
	 * @link org.springblade.modules.beachwaste.enums.WasteMaterialEnum
     */
    private Long wasteMaterial;

    /**
     * 垃圾尺寸分类（对应业务层枚举）
	 * @link org.springblade.modules.beachwaste.enums.WasteSizeEnum
     */
    private Long wasteSize;

    /**
     * 发现网格id
     */
    private Long gridId;

    /**
     * 地理坐标（经度,纬度），采用 WGS84 坐标系
     */
    private Point location;

	/**
	 * 事件发现凭证
	 */
	private String discoveryImagePath;

    /**
     * 发现方式 枚举类（0-AI/1-人工）
	 * @link org.springblade.modules.beachwaste.enums.DiscoveryMethodEnum
     */
    private Long discoveryMethod;

    /**
     * AI 识别置信度值
     */
    private BigDecimal confidence;

    /**
     * 当前处理状态 ID（对应业务层枚举）
	 * @link org.springblade.modules.beachwaste.enums.EventStatusEnum
     */
    private Long eventStatus;

    /**
     * 处理完成上报时间
     */
    private Date handlerReportTime;

    /**
     * 处理后现场照片存储路径
     */
    private String processedImagePath;

    /**
     * 处理人员id
     */
    private Long handlerStaffId;

	/**
	 * 框选图片垃圾位置
	 */
	private String box;

}
