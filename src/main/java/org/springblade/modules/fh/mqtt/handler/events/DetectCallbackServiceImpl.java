package org.springblade.modules.fh.mqtt.handler.events;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.PrecisionModel;
import org.springblade.core.tool.api.R;
import org.springblade.modules.beachwaste.enums.WasteMaterialEnum;
import org.springblade.modules.beachwaste.pojo.dto.DetectResultDTO;
import org.springblade.modules.beachwaste.pojo.entity.BeachLitterMedia;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springblade.modules.beachwaste.pojo.entity.GridSchedule;
import org.springblade.modules.beachwaste.pojo.entity.SpatialGrid;
import org.springblade.modules.beachwaste.service.IBeachLitterMediaService;
import org.springblade.modules.beachwaste.service.IEventService;
import org.springblade.modules.beachwaste.service.IGridScheduleService;
import org.springblade.modules.beachwaste.service.ISpatialGridService;
import org.springblade.modules.fh.mqtt.handler.util.GeometryUtil;
import org.springblade.modules.fh.mqtt.handler.util.RequestBodyParser;
import org.springblade.modules.system.service.ISystemConfigService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 回调检测结果处理服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DetectCallbackServiceImpl implements IDetectCallbackService {

    private final IBeachLitterMediaService beachLitterMediaService;
    private final IEventService eventService;
    private final ISystemConfigService systemConfigService;
    private final IGridScheduleService gridScheduleService;
    private final ISpatialGridService spatialGridService;

    @Override
    public R<String> handleDetectBatchCallback(Object requestBody) {
        log.info("收到垃圾识别批量检测回调，请求体类型: {}", requestBody != null ? requestBody.getClass().getName() : "null");

        // 记录请求体内容，便于调试
        if (requestBody != null) {
            try {
                log.info("请求体内容: {}", JSON.toJSONString(requestBody));
            } catch (Exception e) {
                log.warn("无法序列化请求体内容: {}", e.getMessage());
            }
        }

        List<DetectResultDTO> records;

        try {
			// 解析请求体并返回检查结果集合
            records = parseRequestBody(requestBody);
        } catch (Exception e) {
            log.error("解析请求体时发生异常", e);
            return R.fail("解析请求体失败: " + e.getMessage());
        }

		// 事件对象生成并存储
        return processDetectResults(records);
    }

    /**
     * 解析请求体数据
     *
     * @param requestBody 请求体对象
     * @return 检测结果DTO列表
     */
    private List<DetectResultDTO> parseRequestBody(Object requestBody) {
        return RequestBodyParser.parseRequestBody(requestBody);
    }


    @Override
    public R<String> processDetectResults(List<DetectResultDTO> records) {
        try {
            // 参数校验
            if (records == null || records.isEmpty()) {
                log.warn("回调数据为空");
                return R.fail("回调数据为空");
            }

            log.info("处理批量识别结果，数据条数: {}", records.size());

            // 处理识别结果并生成事件列表
            List<Event> events = new ArrayList<>();
            // 用于存储已处理的box属性，实现去重
            Set<String> processedBoxes = new HashSet<>();
            // 批量收集需要更新的媒体文件
            List<BeachLitterMedia> mediaToUpdate = new ArrayList<>();

            // 遍历处理每条识别结果
            for (DetectResultDTO resultDTO : records) {
                // 跳过无效记录
                if (resultDTO == null || resultDTO.getFileMd5() == null) {
                    log.warn("识别结果对象为空或缺少fileMd5参数");
                    continue;
                }

                // 查找媒体文件记录
                BeachLitterMedia media = getMediaByMd5(resultDTO.getFileMd5());
                if (media == null) {
                    log.warn("未找到对应的媒体文件记录, fileMd5: {}", resultDTO.getFileMd5());
                    continue;
                }

                // 处理空的识别结果
                if (resultDTO.getInfo() == null || resultDTO.getInfo().isEmpty()) {
                    // 更新媒体文件的空结果状态
                    media.setAiStatus(true);
                    media.setAiResult("0");
                    mediaToUpdate.add(media);
                    log.info("AI检测结果info数组为空，跳过事件生成, fileMd5: {}", media.getFileMd5());
                    continue;
                }

                // 记录关键字段值，便于调试
                log.info("处理识别结果，fileMd5: {}, confidence: {}, info数组大小: {}",
                        resultDTO.getFileMd5(),
                        resultDTO.getConfidence(),
                        resultDTO.getInfo().size());

                // 处理识别结果去重逻辑
                List<Object> uniqueInfoList = new ArrayList<>();
                Set<String> currentBoxes = new HashSet<>();

                // 对当前resultDTO的info进行去重
                for (Object item : resultDTO.getInfo()) {
                    DetectResultDTO itemDTO = JSON.parseObject(JSON.toJSONString(item), DetectResultDTO.class);
                    String boxValue = String.valueOf(itemDTO.getBox());

                    // 如果box值未处理过，则添加到唯一列表
                    if (currentBoxes.add(boxValue) && processedBoxes.add(boxValue)) {
                        uniqueInfoList.add(item);
                    }
                }

                // 更新媒体文件的AI识别结果（使用去重后的数量）
                media.setAiStatus(true);
                media.setAiResult(String.valueOf(uniqueInfoList.size()));
                mediaToUpdate.add(media);

                // 为每个去重后的识别项创建事件
                for (Object item : uniqueInfoList) {
                    Event event = createEventFromItem(item, resultDTO, media);
                    if (event != null) {
                        events.add(event);
                    }
                }
            }

            // 批量更新媒体文件
            if (!mediaToUpdate.isEmpty()) {
                beachLitterMediaService.updateBatchById(mediaToUpdate);
            }

            // 批量保存事件
            if (events.isEmpty()) {
                return R.success("没有有效数据需要处理");
            }

            boolean saved = eventService.saveBatch(events);
            if (saved) {
                log.info("成功处理 {} 条识别结果，去重后保存 {} 个事件", records.size(), events.size());
                return R.success("成功处理 " + events.size() + " 条识别结果");
            } else {
                return R.fail("数据保存失败");
            }

        } catch (Exception e) {
            log.error("处理检测批量回调异常", e);
            return R.fail("系统异常: " + e.getMessage());
        }
    }

    /**
     * 从识别项创建事件对象
     * @param item 识别项
     * @param resultDTO 识别结果DTO
     * @param media 媒体文件
     * @return 创建的事件对象
     */
    private Event createEventFromItem(Object item, DetectResultDTO resultDTO, BeachLitterMedia media) {
        try {
            // 将item转换为DetectResultDTO对象
            DetectResultDTO itemDTO = JSON.parseObject(JSON.toJSONString(item), DetectResultDTO.class);

            // 创建事件对象并设置基本属性
            Event event = new Event();

            // 设置基本信息
            event.setDiscoveryTime(DateTime.now());
            event.setDiscoveryImagePath(media.getObjectKey());
            event.setDiscoveryMethod(0L);
            event.setEventStatus(0L);

            // 设置位置信息
            if (resultDTO.getLat() != null && resultDTO.getLon() != null) {
                getGridIdAndLocation(resultDTO, event);
            } else if(itemDTO.getLat() != null && itemDTO.getLon() != null) {
                getGridIdAndLocation(itemDTO, event);
            } else {
                event.setGridId(0L);
                event.setLocation(media.getLocation() != null ? media.getLocation() : "0.0,0.0");
            }

            // 设置垃圾分类信息 设置置信度并转换为百分比格式
            BigDecimal confidencePercentage = null;
            if (itemDTO.getConfidence() != null) {
                int percentage = (int) Math.round(itemDTO.getConfidence() * 100);
                confidencePercentage = BigDecimal.valueOf(percentage);
                event.setConfidence(confidencePercentage);
            }

            // 根据置信度和配置阈值设置处理人员
            setHandlerStaffIfNeeded(event, confidencePercentage);

            // 设置垃圾材质
            Long classId = itemDTO.getClassId() != null ? Long.valueOf(itemDTO.getClassId()) : null;
            WasteMaterialEnum wasteMaterial = WasteMaterialEnum.getByCode(classId);
            if (wasteMaterial != null) {
                event.setWasteMaterial(wasteMaterial.getCode());
            } else {
                // 如果材质类型不在枚举中，使用默认值：无法辨识的材料
                event.setWasteMaterial(WasteMaterialEnum.OTHER.getCode());
            }

            // 设置垃圾大小
            if (itemDTO.getSizeClassId() != null) {
                event.setWasteSize(Long.valueOf(itemDTO.getSizeClassId()));
            } else {
                // 如果为空时设置为最小尺度的垃圾
                event.setWasteSize(4L);
            }

            // 设置边界框
            event.setBox(String.valueOf(itemDTO.getBox()));

            return event;
        } catch (Exception e) {
            log.error("创建事件对象失败", e);
            return null;
        }
    }

    /**
     * 根据检测结果DTO中的经纬度信息，计算并设置事件对象的网格ID和地理位置
     *
     * @param itemDTO 包含经度和纬度信息的检测结果数据传输对象
     * @param event   需要设置网格ID和地理位置的事件对象
     *
     * 该方法通过经纬度调用几何工具类，将计算得到的网格ID转换为长整型后设置到事件对象中，
     * 同时将经纬度转换为地理坐标点格式也设置到事件对象中。这两个操作共同完成了事件地理位置信息的标准化处理。
     */
    private static void getGridIdAndLocation(DetectResultDTO itemDTO, Event event) {
        String lon = itemDTO.getLon();
        String lat = itemDTO.getLat();

        // 使用几何工具类计算网格ID并转换为长整型，设置到事件对象中
        event.setGridId(Long.valueOf(GeometryUtil.getGridId(lon, lat)));

        // 将经纬度转换为地理坐标点格式，设置到事件对象中
        event.setLocation(GeometryUtil.getPoint(lon, lat));
    }

	/**
     * 根据MD5查询媒体文件
     *
     * @param fileMd5 文件MD5值
     * @return 媒体文件实体，如果不存在则返回null
     */
    private BeachLitterMedia getMediaByMd5(String fileMd5) {
        if (fileMd5 == null) {
            return null;
        }

        return beachLitterMediaService.lambdaQuery()
                .eq(BeachLitterMedia::getFileMd5, fileMd5)
                .last("LIMIT 1")
                .one();
    }

    /**
     * 根据置信度和系统配置阈值设置事件处理人员
     * 当置信度大于等于配置的eventNotifyThreshold时，设置当天值班人员为处理人员
     *
     * @param event 事件对象
     * @param confidencePercentage 置信度百分比
     */
    private void setHandlerStaffIfNeeded(Event event, BigDecimal confidencePercentage) {
        try {
            // 获取系统配置中的事件通知阈值
            BigDecimal eventNotifyThreshold = getEventNotifyThreshold();
            if (eventNotifyThreshold == null) {
                log.warn("未配置eventNotifyThreshold，跳过设置处理人员");
                return;
            }

            // 如果置信度为空或小于阈值，不设置处理人员
            if (confidencePercentage == null || confidencePercentage.compareTo(eventNotifyThreshold) < 0) {
                log.debug("置信度{}小于阈值{}，不设置处理人员", confidencePercentage, eventNotifyThreshold);
                return;
            }

            // 获取事件所在网格当天的值班人员ID
            Long handlerStaffId = getTodayDutyStaffId(event.getGridId());
            if (handlerStaffId != null) {
                event.setHandlerStaffId(handlerStaffId);
                log.info("置信度{}达到阈值{}，设置网格{}当天值班人员{}为处理人员",
                        confidencePercentage, eventNotifyThreshold, event.getGridId(), handlerStaffId);
            } else {
                log.warn("网格{}当天无值班人员，则先设置为当前网格的网格管理员", event.getGridId());
                // 获取网格信息
                SpatialGrid grid = spatialGridService.getById(event.getGridId());
                if (grid != null && grid.getUserId() != null) {
                    event.setHandlerStaffId(grid.getUserId());
                    log.info("设置网格{}的管理员{}为处理人员", event.getGridId(), grid.getUserId());
                } else {
                    log.warn("网格{}无管理员信息，无法设置处理人员", event.getGridId());
                }
            }
        } catch (Exception e) {
            log.error("设置事件处理人员时发生异常", e);
        }
    }

    /**
     * 从系统配置中获取事件通知阈值
     *
     * @return 事件通知阈值，如果未配置或配置无效则返回null
     */
    private BigDecimal getEventNotifyThreshold() {
        try {
            R<Map<String, String>> configResult = systemConfigService.getGlobalConfigMap();
            if (!configResult.isSuccess() || configResult.getData() == null) {
                log.warn("获取系统配置失败");
                return null;
            }

            String thresholdStr = configResult.getData().get("eventNotifyThreshold");
            if (thresholdStr == null || thresholdStr.trim().isEmpty()) {
                log.warn("系统配置中未找到eventNotifyThreshold配置项");
                return null;
            }

            return new BigDecimal(thresholdStr.trim());
        } catch (NumberFormatException e) {
            log.error("eventNotifyThreshold配置值格式错误: {}", e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("获取eventNotifyThreshold配置时发生异常", e);
            return null;
        }
    }

    /**
     * 获取指定网格当天的值班人员ID
     *
     * @param gridId 网格ID
     * @return 值班人员ID，如果没有值班人员则返回null
     */
    private Long getTodayDutyStaffId(Long gridId) {
        if (gridId == null || gridId == 0L) {
            return null;
        }

        try {
            List<GridSchedule> schedules = gridScheduleService.getScheduleByGridAndDate(gridId, LocalDate.now());

            if (schedules != null && !schedules.isEmpty()) {
                // 返回第一个排班记录的人员ID
                return schedules.get(0).getStaffId();
            }
        } catch (Exception e) {
            log.error("获取网格{}当天值班人员时发生异常", gridId, e);
        }

        return null;
    }

}
